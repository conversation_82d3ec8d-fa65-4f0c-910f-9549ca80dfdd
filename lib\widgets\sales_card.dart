import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:wicker/services/time_ago_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class SalesCard extends StatelessWidget {
  final Map<String, dynamic> salesData;

  const SalesCard({super.key, required this.salesData});

  @override
  Widget build(BuildContext context) {
    final String status = salesData['status'] ?? 'pending';
    final double totalAmount =
        (salesData['total_amount'] as num?)?.toDouble() ?? 0.0;
    final String orderDate = TimeAgoService.format(
      salesData['created_at']?['\$date'],
    );
    final buyerDetails =
        salesData['buyer_details'] as Map<String, dynamic>? ?? {};
    final buyerName = buyerDetails['username'] ?? 'Unknown Buyer';
    final List<dynamic> items = salesData['items'] as List<dynamic>? ?? [];

    Color statusColor;
    IconData statusIcon;
    switch (status.toLowerCase()) {
      case 'paid':
        statusColor = Colors.green;
        statusIcon = EvaIcons.checkmarkCircle2;
        break;
      case 'pending':
        statusColor = Colors.orange;
        statusIcon = EvaIcons.clockOutline;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = EvaIcons.questionMarkCircleOutline;
    }

    return NeuCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(EvaIcons.person, color: Colors.grey[700]),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Sale to: $buyerName',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              NeuChip(
                label: status.toUpperCase(),
                icon: statusIcon,
                backgroundColor: statusColor,
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              'Order #${salesData['_id']['\$oid'].substring(0, 8)}',
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ),
          const Divider(height: 24, thickness: 2, color: Colors.black),

          // --- REFACTORED: Display list of items instead of just the count ---
          ...items.map((item) => _buildSalesItem(item)).toList(),

          // --- End of REFACTOR ---
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(orderDate, style: const TextStyle(color: Colors.grey)),
              Text(
                'Total: GHS ${totalAmount.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  color: Color(0xFF6C5CE7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // --- NEW: Helper widget to display a single item in the sales card ---
  Widget _buildSalesItem(Map<String, dynamic> item) {
    final details = item['product_details'] as Map<String, dynamic>;
    final name = details['product_name'] ?? 'N/A';
    final quantity = item['quantity'] ?? 0;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Text('•', style: TextStyle(color: Colors.grey[700], fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(child: Text(name, style: const TextStyle(fontSize: 14))),
          Text(
            'x$quantity',
            style: TextStyle(
              color: Colors.grey[800],
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
